'use client'

import Link from 'next/link'
import { 
  GitMerge, 
  Database, 
  ArrowRight, 
  CheckCircle,
  Clock,
  AlertTriangle,
  Plus,
  Play,
  Pause,
  Settings,
  BarChart3,
  Users,
  Building2
} from 'lucide-react'

export default function TaskManagement() {
  const aggregationTasks = [
    {
      id: 1,
      name: '人口数据汇聚',
      description: '整合公安、民政、人社等部门人口相关数据',
      sources: ['公安局', '民政局', '人社局'],
      status: 'running',
      progress: 85,
      lastRun: '2分钟前',
      records: '2.4M',
      schedule: '每小时'
    },
    {
      id: 2,
      name: '企业信息汇聚',
      description: '汇聚市场监管、税务、工信等部门企业数据',
      sources: ['市场监管局', '税务局', '工信局'],
      status: 'completed',
      progress: 100,
      lastRun: '1小时前',
      records: '156K',
      schedule: '每日'
    },
    {
      id: 3,
      name: '经济指标汇聚',
      description: '统计、财政、发改等部门经济数据整合',
      sources: ['统计局', '财政局', '发改委'],
      status: 'error',
      progress: 45,
      lastRun: '3小时前',
      records: '89K',
      schedule: '每日'
    },
    {
      id: 4,
      name: '环境数据汇聚',
      description: '环保、水务、应急等部门环境监测数据',
      sources: ['生态环境局', '水务局', '应急管理局'],
      status: 'scheduled',
      progress: 0,
      lastRun: '明天 09:00',
      records: '234K',
      schedule: '每周'
    }
  ]

  const quickStats = [
    { label: '汇聚任务', value: '8', trend: '+2', icon: GitMerge, color: 'orange' },
    { label: '数据源', value: '24', trend: '+3', icon: Database, color: 'blue' },
    { label: '汇聚记录', value: '3.2M', trend: '+18%', icon: BarChart3, color: 'green' },
    { label: '参与部门', value: '15', trend: '+1', icon: Building2, color: 'purple' }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Clock className="w-5 h-5 text-blue-500 animate-spin" />
      case 'completed': return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'error': return <AlertTriangle className="w-5 h-5 text-red-500" />
      case 'scheduled': return <Clock className="w-5 h-5 text-gray-500" />
      default: return <Clock className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'running': return '运行中'
      case 'completed': return '已完成'
      case 'error': return '错误'
      case 'scheduled': return '已计划'
      default: return '未知'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-blue-100 text-blue-700'
      case 'completed': return 'bg-green-100 text-green-700'
      case 'error': return 'bg-red-100 text-red-700'
      case 'scheduled': return 'bg-gray-100 text-gray-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  return (
    <div>
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">任务管理中心</h1>
        <p className="text-xl text-gray-600">管理和监控各采集Agent汇聚至缓冲通道的任务</p>
      </div>

      {/* 快速统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {quickStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-sm text-green-600 font-medium">{stat.trend}</p>
                </div>
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <button className="bg-orange-600 text-white px-6 py-3 rounded-xl hover:bg-orange-700 transition-colors flex items-center space-x-2 font-medium">
            <Plus className="w-5 h-5" />
            <span>创建汇聚任务</span>
          </button>
          <button className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium">
            <Settings className="w-5 h-5" />
            <span>汇聚规则</span>
          </button>
        </div>
      </div>

      {/* 汇聚任务列表 */}
      <div className="space-y-6">
        {aggregationTasks.map((task, index) => (
          <div
            key={task.id}
            className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
          >
            <div className="p-6">
              {/* 头部 */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-xl font-bold text-gray-900">{task.name}</h3>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(task.status)}`}>
                      {getStatusText(task.status)}
                    </span>
                    {getStatusIcon(task.status)}
                  </div>
                  <p className="text-gray-600 mb-3">{task.description}</p>
                  <div className="flex items-center space-x-4 text-sm">
                    <span className="text-gray-500">数据源:</span>
                    <div className="flex flex-wrap gap-2">
                      {task.sources.map((source, idx) => (
                        <span key={idx} className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">
                          {source}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-amber-500 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <GitMerge className="w-8 h-8 text-white" />
                </div>
              </div>

              {/* 进度条 */}
              {task.status === 'running' && (
                <div className="mb-4">
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>汇聚进度</span>
                    <span>{task.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-orange-500 to-amber-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${task.progress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {/* 统计信息 */}
              <div className="grid grid-cols-3 gap-4 mb-4 p-4 bg-gray-50/50 rounded-xl">
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">汇聚记录</p>
                  <p className="text-lg font-bold text-gray-900">{task.records}</p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">执行频率</p>
                  <p className="text-sm font-medium text-gray-900">{task.schedule}</p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">最后执行</p>
                  <p className="text-sm font-medium text-gray-900">{task.lastRun}</p>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {task.status === 'running' ? (
                    <button className="text-red-600 hover:text-red-700 font-medium text-sm transition-colors flex items-center space-x-1">
                      <Pause className="w-4 h-4" />
                      <span>暂停</span>
                    </button>
                  ) : (
                    <button className="text-green-600 hover:text-green-700 font-medium text-sm transition-colors flex items-center space-x-1">
                      <Play className="w-4 h-4" />
                      <span>启动</span>
                    </button>
                  )}
                  <button className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors">
                    查看日志
                  </button>
                </div>
                <Link
                  href={`/aggregation/task/${task.id}`}
                  className="text-orange-600 hover:text-orange-700 font-medium flex items-center space-x-1 transition-colors text-sm"
                >
                  <span>管理任务</span>
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
