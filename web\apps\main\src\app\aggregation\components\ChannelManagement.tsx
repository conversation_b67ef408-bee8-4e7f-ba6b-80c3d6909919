'use client'

import { 
  Server, 
  Database, 
  Activity, 
  Cpu, 
  HardDrive, 
  Network, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Settings,
  BarChart3,
  Zap,
  Globe,
  FileText,
  Layers
} from 'lucide-react'

export default function ChannelManagement() {
  const channels = [
    {
      id: 1,
      name: 'Kafka 集群',
      type: 'kafka',
      status: 'healthy',
      description: '高吞吐量消息队列，处理实时数据流',
      nodes: 3,
      throughput: '2.5K msg/s',
      storage: '1.2TB',
      uptime: '99.9%',
      cpu: 45,
      memory: 68,
      network: 32,
      partitions: 24,
      consumers: 8
    },
    {
      id: 2,
      name: 'RabbitMQ 集群',
      type: 'rabbitmq',
      status: 'healthy',
      description: '可靠消息传递，支持复杂路由',
      nodes: 2,
      throughput: '1.8K msg/s',
      storage: '450GB',
      uptime: '99.8%',
      cpu: 38,
      memory: 55,
      network: 28,
      queues: 16,
      exchanges: 12
    },
    {
      id: 3,
      name: 'Redis 缓存',
      type: 'redis',
      status: 'warning',
      description: '高性能内存数据库，用于缓存和会话存储',
      nodes: 2,
      throughput: '15K ops/s',
      storage: '128GB',
      uptime: '99.5%',
      cpu: 72,
      memory: 85,
      network: 45,
      keys: '2.4M',
      hitRate: '94.2%'
    },
    {
      id: 4,
      name: '中间库数据库',
      type: 'database',
      status: 'healthy',
      description: 'PostgreSQL 集群，存储处理后的结构化数据',
      nodes: 2,
      throughput: '850 TPS',
      storage: '5.6TB',
      uptime: '99.9%',
      cpu: 52,
      memory: 71,
      network: 35,
      connections: 245,
      tables: 156
    },
    {
      id: 5,
      name: 'FTP 文件服务',
      type: 'ftp',
      status: 'error',
      description: '文件传输服务，处理大文件和批量数据',
      nodes: 1,
      throughput: '120 MB/s',
      storage: '2.8TB',
      uptime: '98.2%',
      cpu: 25,
      memory: 42,
      network: 78,
      files: '45K',
      transfers: 156
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-500" />
      case 'error': return <AlertTriangle className="w-5 h-5 text-red-500" />
      default: return <Clock className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'healthy': return '健康'
      case 'warning': return '警告'
      case 'error': return '错误'
      default: return '未知'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-100 text-green-700'
      case 'warning': return 'bg-yellow-100 text-yellow-700'
      case 'error': return 'bg-red-100 text-red-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'kafka': return <Layers className="w-8 h-8 text-white" />
      case 'rabbitmq': return <Network className="w-8 h-8 text-white" />
      case 'redis': return <Zap className="w-8 h-8 text-white" />
      case 'database': return <Database className="w-8 h-8 text-white" />
      case 'ftp': return <FileText className="w-8 h-8 text-white" />
      default: return <Server className="w-8 h-8 text-white" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'kafka': return 'from-purple-500 to-indigo-500'
      case 'rabbitmq': return 'from-blue-500 to-cyan-500'
      case 'redis': return 'from-red-500 to-pink-500'
      case 'database': return 'from-green-500 to-emerald-500'
      case 'ftp': return 'from-orange-500 to-amber-500'
      default: return 'from-gray-500 to-slate-500'
    }
  }

  const overallStats = [
    { label: '总通道数', value: '5', icon: Server, color: 'blue' },
    { label: '健康通道', value: '3', icon: CheckCircle, color: 'green' },
    { label: '总吞吐量', value: '6.2K/s', icon: Activity, color: 'purple' },
    { label: '平均可用性', value: '99.5%', icon: BarChart3, color: 'orange' }
  ]

  return (
    <div>
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">通道管理中心</h1>
        <p className="text-xl text-gray-600">管理各缓冲通道的运行情况、性能指标、部署架构和集群状态</p>
      </div>

      {/* 总体统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {overallStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 通道列表 */}
      <div className="space-y-6">
        {channels.map((channel) => (
          <div
            key={channel.id}
            className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
          >
            <div className="p-6">
              {/* 头部 */}
              <div className="flex items-start justify-between mb-6">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-xl font-bold text-gray-900">{channel.name}</h3>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(channel.status)}`}>
                      {getStatusText(channel.status)}
                    </span>
                    {getStatusIcon(channel.status)}
                  </div>
                  <p className="text-gray-600 mb-3">{channel.description}</p>
                  <div className="flex items-center space-x-6 text-sm text-gray-500">
                    <span>节点数: {channel.nodes}</span>
                    <span>可用性: {channel.uptime}</span>
                    <span>吞吐量: {channel.throughput}</span>
                  </div>
                </div>
                <div className={`w-16 h-16 bg-gradient-to-br ${getTypeColor(channel.type)} rounded-2xl flex items-center justify-center flex-shrink-0`}>
                  {getTypeIcon(channel.type)}
                </div>
              </div>

              {/* 性能指标 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-gray-50/50 rounded-xl p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600 flex items-center space-x-1">
                      <Cpu className="w-4 h-4" />
                      <span>CPU 使用率</span>
                    </span>
                    <span className="text-sm font-medium">{channel.cpu}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 ${
                        channel.cpu > 80 ? 'bg-red-500' : channel.cpu > 60 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${channel.cpu}%` }}
                    ></div>
                  </div>
                </div>

                <div className="bg-gray-50/50 rounded-xl p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600 flex items-center space-x-1">
                      <HardDrive className="w-4 h-4" />
                      <span>内存使用率</span>
                    </span>
                    <span className="text-sm font-medium">{channel.memory}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 ${
                        channel.memory > 80 ? 'bg-red-500' : channel.memory > 60 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${channel.memory}%` }}
                    ></div>
                  </div>
                </div>

                <div className="bg-gray-50/50 rounded-xl p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600 flex items-center space-x-1">
                      <Network className="w-4 h-4" />
                      <span>网络使用率</span>
                    </span>
                    <span className="text-sm font-medium">{channel.network}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 ${
                        channel.network > 80 ? 'bg-red-500' : channel.network > 60 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${channel.network}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              {/* 详细信息 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 p-4 bg-gray-50/50 rounded-xl">
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">存储空间</p>
                  <p className="text-sm font-bold text-gray-900">{channel.storage}</p>
                </div>
                {channel.type === 'kafka' && (
                  <>
                    <div className="text-center">
                      <p className="text-xs text-gray-600 mb-1">分区数</p>
                      <p className="text-sm font-bold text-gray-900">{channel.partitions}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-600 mb-1">消费者</p>
                      <p className="text-sm font-bold text-gray-900">{channel.consumers}</p>
                    </div>
                  </>
                )}
                {channel.type === 'rabbitmq' && (
                  <>
                    <div className="text-center">
                      <p className="text-xs text-gray-600 mb-1">队列数</p>
                      <p className="text-sm font-bold text-gray-900">{channel.queues}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-600 mb-1">交换器</p>
                      <p className="text-sm font-bold text-gray-900">{channel.exchanges}</p>
                    </div>
                  </>
                )}
                {channel.type === 'redis' && (
                  <>
                    <div className="text-center">
                      <p className="text-xs text-gray-600 mb-1">键数量</p>
                      <p className="text-sm font-bold text-gray-900">{channel.keys}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-600 mb-1">命中率</p>
                      <p className="text-sm font-bold text-gray-900">{channel.hitRate}</p>
                    </div>
                  </>
                )}
                {channel.type === 'database' && (
                  <>
                    <div className="text-center">
                      <p className="text-xs text-gray-600 mb-1">连接数</p>
                      <p className="text-sm font-bold text-gray-900">{channel.connections}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-600 mb-1">表数量</p>
                      <p className="text-sm font-bold text-gray-900">{channel.tables}</p>
                    </div>
                  </>
                )}
                {channel.type === 'ftp' && (
                  <>
                    <div className="text-center">
                      <p className="text-xs text-gray-600 mb-1">文件数</p>
                      <p className="text-sm font-bold text-gray-900">{channel.files}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-600 mb-1">传输任务</p>
                      <p className="text-sm font-bold text-gray-900">{channel.transfers}</p>
                    </div>
                  </>
                )}
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <button className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors flex items-center space-x-1">
                    <BarChart3 className="w-4 h-4" />
                    <span>性能监控</span>
                  </button>
                  <button className="text-green-600 hover:text-green-700 font-medium text-sm transition-colors flex items-center space-x-1">
                    <Settings className="w-4 h-4" />
                    <span>配置管理</span>
                  </button>
                  <button className="text-purple-600 hover:text-purple-700 font-medium text-sm transition-colors">
                    查看日志
                  </button>
                </div>
                <button className="text-orange-600 hover:text-orange-700 font-medium text-sm transition-colors">
                  集群详情
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
